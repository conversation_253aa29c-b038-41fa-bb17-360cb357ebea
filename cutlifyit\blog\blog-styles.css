/* Blog Article Styles */
.article-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4rem 0;
}

.article-meta-info {
    background: rgba(255,255,255,0.1);
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 2rem;
}

.blog-article {
    font-size: 1.1rem;
    line-height: 1.8;
}

.blog-article h2 {
    color: #333;
    margin-top: 2.5rem;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #7c3aed;
}

.blog-article h3 {
    color: #555;
    margin-top: 2rem;
    margin-bottom: 1rem;
}

.blog-article ul {
    margin: 1.5rem 0;
}

.blog-article li {
    margin-bottom: 0.5rem;
}

.article-navigation {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 2rem;
    margin-top: 3rem;
}

.related-articles {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 2rem;
    margin-top: 2rem;
}

.breadcrumb-custom {
    background: transparent;
    padding: 0;
    margin-bottom: 1rem;
}

.breadcrumb-custom .breadcrumb-item + .breadcrumb-item::before {
    content: "›";
    color: rgba(255,255,255,0.7);
}

.breadcrumb-custom a {
    color: rgba(255,255,255,0.8);
    text-decoration: none;
}

.breadcrumb-custom a:hover {
    color: white;
}

/* Blog Index Styles */
.hero-bg-gradient {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    z-index: -1;
}

.min-vh-50 { 
    min-height: 50vh; 
}

.stat-item {
    text-align: center;
    color: white;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    display: block;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.featured-card {
    transition: transform 0.3s ease;
}

.featured-card:hover {
    transform: translateY(-5px);
}

.blog-card {
    transition: all 0.3s ease;
    border: none;
}

.blog-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
}

.blog-card .card-header {
    border-bottom: none;
}

.article-meta {
    font-size: 0.85rem;
}

@keyframes fadeIn {
    from { 
        opacity: 0; 
        transform: translateY(20px); 
    }
    to { 
        opacity: 1; 
        transform: translateY(0); 
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .article-hero {
        padding: 3rem 0;
    }
    
    .blog-article {
        font-size: 1rem;
    }
    
    .stat-item {
        margin-bottom: 1rem;
    }
}
