// ===== Main JavaScript File =====

// DOM Content Loaded Event
document.addEventListener('DOMContentLoaded', function() {
    // Add loading animation to main sections
    const sections = document.querySelectorAll('section, .converter-container');
    sections.forEach(section => {
        section.classList.add('loading');
    });
    
    initializeApp();
    
    // Initialize intersection observer for animations
    initializeScrollAnimations();
});

// Initialize Application
function initializeApp() {
    initializeCounters();
    initializeSmoothScrolling();
    initializeNavbarScroll();
    initializeMobileMenu();
    initializeConverterTabs();
    loadConverterContent();
    initializeHeroDemo();
    initializeEnhancedInteractions();
    handleInitialHash(); // Handle hash on page load

    // Debug: Log that the app is initialized
    console.log('CutlifyIt app initialized successfully');
}

// ===== Counter Animation =====
function initializeCounters() {
    const counters = document.querySelectorAll('.stat-number[data-count]');
    
    const observerOptions = {
        threshold: 0.5,
        rootMargin: '0px 0px -100px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCounter(entry.target);
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);
    
    counters.forEach(counter => {
        observer.observe(counter);
    });
}

function animateCounter(element) {
    const target = parseInt(element.getAttribute('data-count'));
    const duration = 2000; // 2 seconds
    const step = target / (duration / 16); // 60fps
    let current = 0;
    
    const timer = setInterval(() => {
        current += step;
        if (current >= target) {
            current = target;
            clearInterval(timer);
        }
        
        // Format number with commas
        element.textContent = Math.floor(current).toLocaleString();
    }, 16);
}

// ===== Smooth Scrolling =====
function initializeSmoothScrolling() {
    const links = document.querySelectorAll('a[href^="#"]');

    links.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            const targetId = this.getAttribute('href').substring(1); // Remove the #
            const targetElement = document.querySelector('#' + targetId);

            console.log('Clicked link with target:', targetId); // Debug

            // Check if this is a converter tab
            const converterTabs = ['units', 'currency', 'colors', 'encoding', 'timezone'];
            if (converterTabs.includes(targetId)) {
                console.log('Activating converter tab:', targetId); // Debug
                // Activate the corresponding tab
                const tabButton = document.querySelector(`#${targetId}-tab`);
                if (tabButton) {
                    try {
                        // Use Bootstrap's tab API to show the tab
                        if (typeof bootstrap !== 'undefined' && bootstrap.Tab) {
                            const tab = new bootstrap.Tab(tabButton);
                            tab.show();
                        } else {
                            // Fallback: manually activate tab
                            activateTabManually(targetId);
                        }
                    } catch (error) {
                        console.log('Bootstrap Tab API error, using fallback:', error);
                        activateTabManually(targetId);
                    }
                }

                // Scroll to the converters section
                const convertersSection = document.querySelector('#converters');
                if (convertersSection) {
                    const headerHeight = document.querySelector('.header').offsetHeight;
                    const targetPosition = convertersSection.offsetTop - headerHeight - 20;

                    setTimeout(() => {
                        window.scrollTo({
                            top: targetPosition,
                            behavior: 'smooth'
                        });
                    }, 100); // Small delay to allow tab to activate
                }
            } else if (targetElement) {
                // Regular anchor scrolling
                const headerHeight = document.querySelector('.header').offsetHeight;
                const targetPosition = targetElement.offsetTop - headerHeight - 20;

                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }

            // Close mobile menu if open
            closeMobileMenu();
        });
    });
}

// ===== Mobile Menu =====
function initializeMobileMenu() {
    const navbarToggler = document.querySelector('.navbar-toggler');
    const navbarCollapse = document.querySelector('.navbar-collapse');
    const mobileMenuClose = document.getElementById('mobileMenuClose');
    const navLinks = document.querySelectorAll('.navbar-nav .nav-link');

    // Toggle mobile menu
    if (navbarToggler) {
        navbarToggler.addEventListener('click', function() {
            toggleMobileMenu();
        });
    }

    // Close mobile menu button
    if (mobileMenuClose) {
        mobileMenuClose.addEventListener('click', function() {
            closeMobileMenu();
        });
    }

    // Close menu when clicking nav links
    navLinks.forEach(link => {
        link.addEventListener('click', function() {
            // Add small delay to allow smooth scrolling to start
            setTimeout(() => {
                closeMobileMenu();
            }, 100);
        });
    });

    // Close menu when clicking outside (on overlay)
    if (navbarCollapse) {
        navbarCollapse.addEventListener('click', function(e) {
            if (e.target === navbarCollapse) {
                closeMobileMenu();
            }
        });
    }

    // Close menu on escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeMobileMenu();
        }
    });
}

function toggleMobileMenu() {
    const navbarCollapse = document.querySelector('.navbar-collapse');
    const body = document.body;

    if (navbarCollapse) {
        if (navbarCollapse.classList.contains('show')) {
            closeMobileMenu();
        } else {
            openMobileMenu();
        }
    }
}

function openMobileMenu() {
    const navbarCollapse = document.querySelector('.navbar-collapse');
    const body = document.body;

    if (navbarCollapse) {
        navbarCollapse.classList.add('show');
        body.style.overflow = 'hidden'; // Prevent background scrolling
    }
}

function closeMobileMenu() {
    const navbarCollapse = document.querySelector('.navbar-collapse');
    const body = document.body;

    if (navbarCollapse && navbarCollapse.classList.contains('show')) {
        navbarCollapse.classList.remove('show');
        body.style.overflow = ''; // Restore scrolling
    }
}

// ===== Manual Tab Activation (Fallback) =====
function activateTabManually(targetId) {
    // Remove active class from all tabs and tab panes
    const allTabs = document.querySelectorAll('#converterTabs .nav-link');
    const allPanes = document.querySelectorAll('.tab-pane');

    allTabs.forEach(tab => tab.classList.remove('active'));
    allPanes.forEach(pane => {
        pane.classList.remove('active', 'show');
    });

    // Activate the target tab and pane
    const targetTab = document.querySelector(`#${targetId}-tab`);
    const targetPane = document.querySelector(`#${targetId}`);

    if (targetTab) {
        targetTab.classList.add('active');
    }

    if (targetPane) {
        targetPane.classList.add('active', 'show');
    }
}

// ===== Handle Initial Hash =====
function handleInitialHash() {
    // Check if there's a hash in the URL when the page loads
    const hash = window.location.hash;
    if (hash) {
        const targetId = hash.substring(1); // Remove the #
        const converterTabs = ['units', 'currency', 'colors', 'encoding', 'timezone'];

        if (converterTabs.includes(targetId)) {
            // Activate the corresponding tab
            const tabButton = document.querySelector(`#${targetId}-tab`);
            if (tabButton) {
                try {
                    // Use Bootstrap's tab API to show the tab
                    if (typeof bootstrap !== 'undefined' && bootstrap.Tab) {
                        const tab = new bootstrap.Tab(tabButton);
                        tab.show();
                    } else {
                        // Fallback: manually activate tab
                        activateTabManually(targetId);
                    }
                } catch (error) {
                    console.log('Bootstrap Tab API error, using fallback:', error);
                    activateTabManually(targetId);
                }

                // Scroll to the converters section after a short delay
                setTimeout(() => {
                    const convertersSection = document.querySelector('#converters');
                    if (convertersSection) {
                        const headerHeight = document.querySelector('.header').offsetHeight;
                        const targetPosition = convertersSection.offsetTop - headerHeight - 20;

                        window.scrollTo({
                            top: targetPosition,
                            behavior: 'smooth'
                        });
                    }
                }, 300); // Delay to allow tab content to load
            }
        }
    }
}

// ===== Navbar Scroll Effect =====
function initializeNavbarScroll() {
    const header = document.querySelector('.header');
    let lastScrollTop = 0;
    
    window.addEventListener('scroll', () => {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        
        if (scrollTop > 100) {
            header.style.background = 'rgba(255, 255, 255, 0.95)';
            header.style.backdropFilter = 'blur(10px)';
        } else {
            header.style.background = 'var(--bg-primary)';
            header.style.backdropFilter = 'none';
        }
        
        lastScrollTop = scrollTop;
    });
}

// ===== Converter Tabs Management =====
function initializeConverterTabs() {
    const tabButtons = document.querySelectorAll('#converterTabs button[data-bs-toggle="pill"]');
    
    tabButtons.forEach(button => {
        button.addEventListener('shown.bs.tab', function(e) {
            const targetTab = e.target.getAttribute('data-bs-target');
            const converterName = targetTab.replace('#', '');
            loadSpecificConverter(converterName);
        });
    });
}

// ===== Load Converter Content =====
function loadConverterContent() {
    // Load default converter (units)
    loadSpecificConverter('units');
}

function loadSpecificConverter(converterName) {
    const converterContent = document.querySelector(`#${converterName} .converter-content`);
    
    if (!converterContent) return;
    
    // Clear existing content
    converterContent.innerHTML = '';
    
    switch(converterName) {
        case 'units':
            loadUnitConverter(converterContent);
            break;
        case 'currency':
            loadCurrencyConverter(converterContent);
            break;

        case 'colors':
            loadColorConverter(converterContent);
            break;
        case 'encoding':
            loadEncodingConverter(converterContent);
            break;
        case 'timezone':
            loadTimezoneConverter(converterContent);
            break;
    }
}

// ===== Unit Converter =====
function loadUnitConverter(container) {
    container.innerHTML = `
        <div class="converter-form">
            <div class="mb-4">
                <label class="form-label fw-semibold">Conversion Category</label>
                <select class="form-select" id="unitCategory">
                    <option value="length">Length</option>
                    <option value="weight">Weight</option>
                    <option value="volume">Volume</option>
                    <option value="area">Area</option>
                    <option value="temperature">Temperature</option>
                    <option value="speed">Speed</option>
                    <option value="time">Time</option>
                    <option value="data">Data Storage</option>
                    <option value="energy">Energy</option>
                </select>
            </div>
            
            <div class="converter-row">
                <div class="converter-input-group">
                    <label class="converter-label">From</label>
                    <input type="number" class="converter-value" id="fromValue" placeholder="Enter value" value="1">
                    <select class="converter-unit" id="fromUnit">
                        <!-- Units will be populated based on category -->
                    </select>
                </div>
                
                <button class="converter-swap" id="swapUnits" title="Swap units">
                    <i class="fas fa-exchange-alt"></i>
                </button>
                
                <div class="converter-output-group">
                    <label class="converter-label">To</label>
                    <input type="number" class="converter-value" id="toValue" placeholder="Result" readonly>
                    <select class="converter-unit" id="toUnit">
                        <!-- Units will be populated based on category -->
                    </select>
                </div>
            </div>
            
            <div class="converter-result" id="unitResult" style="display: none;">
                <div class="result-value" id="resultValue">1</div>
                <div class="result-label" id="resultLabel">meter = 3.28084 feet</div>
                <button class="copy-btn" id="copyResult">
                    <i class="fas fa-copy"></i> Copy Result
                </button>
            </div>
            
            <div class="quick-convert">
                <div class="fw-semibold mb-2">Quick Convert:</div>
                <button class="quick-btn" data-value="1">1</button>
                <button class="quick-btn" data-value="10">10</button>
                <button class="quick-btn" data-value="100">100</button>
                <button class="quick-btn" data-value="1000">1000</button>
            </div>
        </div>
    `;
    
    initializeUnitConverter();
}

function initializeUnitConverter() {
    const categorySelect = document.getElementById('unitCategory');
    const fromUnit = document.getElementById('fromUnit');
    const toUnit = document.getElementById('toUnit');
    const fromValue = document.getElementById('fromValue');
    const toValue = document.getElementById('toValue');
    const swapBtn = document.getElementById('swapUnits');
    const copyBtn = document.getElementById('copyResult');
    const quickBtns = document.querySelectorAll('.quick-btn');
    
    // Unit definitions
    const units = {
        length: {
            meter: { name: 'Meter', factor: 1 },
            kilometer: { name: 'Kilometer', factor: 1000 },
            centimeter: { name: 'Centimeter', factor: 0.01 },
            millimeter: { name: 'Millimeter', factor: 0.001 },
            inch: { name: 'Inch', factor: 0.0254 },
            foot: { name: 'Foot', factor: 0.3048 },
            yard: { name: 'Yard', factor: 0.9144 },
            mile: { name: 'Mile', factor: 1609.344 }
        },
        weight: {
            kilogram: { name: 'Kilogram', factor: 1 },
            gram: { name: 'Gram', factor: 0.001 },
            pound: { name: 'Pound', factor: 0.453592 },
            ounce: { name: 'Ounce', factor: 0.0283495 },
            ton: { name: 'Metric Ton', factor: 1000 },
            stone: { name: 'Stone', factor: 6.35029 }
        },
        volume: {
            liter: { name: 'Liter', factor: 1 },
            milliliter: { name: 'Milliliter', factor: 0.001 },
            gallon: { name: 'Gallon (US)', factor: 3.78541 },
            quart: { name: 'Quart (US)', factor: 0.946353 },
            pint: { name: 'Pint (US)', factor: 0.473176 },
            cup: { name: 'Cup (US)', factor: 0.236588 },
            fluid_ounce: { name: 'Fluid Ounce (US)', factor: 0.0295735 }
        },
        temperature: {
            celsius: { name: 'Celsius', factor: 1 },
            fahrenheit: { name: 'Fahrenheit', factor: 1 },
            kelvin: { name: 'Kelvin', factor: 1 }
        }
    };
    
    // Populate units based on category
    function populateUnits(category) {
        const categoryUnits = units[category] || units.length;
        
        fromUnit.innerHTML = '';
        toUnit.innerHTML = '';
        
        Object.keys(categoryUnits).forEach(key => {
            const unit = categoryUnits[key];
            fromUnit.innerHTML += `<option value="${key}">${unit.name}</option>`;
            toUnit.innerHTML += `<option value="${key}">${unit.name}</option>`;
        });
        
        // Set default values
        if (Object.keys(categoryUnits).length > 1) {
            toUnit.selectedIndex = 1;
        }
        
        convertUnits();
    }
    
    // Convert units
    function convertUnits() {
        const category = categorySelect.value;
        const fromUnitKey = fromUnit.value;
        const toUnitKey = toUnit.value;
        const value = parseFloat(fromValue.value) || 0;
        
        if (!units[category]) return;
        
        let result;
        
        if (category === 'temperature') {
            result = convertTemperature(value, fromUnitKey, toUnitKey);
        } else {
            const fromFactor = units[category][fromUnitKey]?.factor || 1;
            const toFactor = units[category][toUnitKey]?.factor || 1;
            result = (value * fromFactor) / toFactor;
        }
        
        toValue.value = result.toFixed(6).replace(/\.?0+$/, '');
        
        // Update result display
        const resultDiv = document.getElementById('unitResult');
        const resultValue = document.getElementById('resultValue');
        const resultLabel = document.getElementById('resultLabel');
        
        resultValue.textContent = toValue.value;
        resultLabel.textContent = `${value} ${units[category][fromUnitKey]?.name || fromUnitKey} = ${toValue.value} ${units[category][toUnitKey]?.name || toUnitKey}`;
        resultDiv.style.display = 'block';
    }
    
    // Temperature conversion
    function convertTemperature(value, from, to) {
        // Convert to Celsius first
        let celsius;
        switch(from) {
            case 'fahrenheit':
                celsius = (value - 32) * 5/9;
                break;
            case 'kelvin':
                celsius = value - 273.15;
                break;
            default:
                celsius = value;
        }
        
        // Convert from Celsius to target
        switch(to) {
            case 'fahrenheit':
                return celsius * 9/5 + 32;
            case 'kelvin':
                return celsius + 273.15;
            default:
                return celsius;
        }
    }
    
    // Event listeners
    categorySelect.addEventListener('change', () => populateUnits(categorySelect.value));
    fromUnit.addEventListener('change', convertUnits);
    toUnit.addEventListener('change', convertUnits);
    fromValue.addEventListener('input', convertUnits);
    
    swapBtn.addEventListener('click', () => {
        const tempUnit = fromUnit.value;
        fromUnit.value = toUnit.value;
        toUnit.value = tempUnit;
        convertUnits();
    });
    
    copyBtn.addEventListener('click', () => {
        navigator.clipboard.writeText(toValue.value).then(() => {
            copyBtn.innerHTML = '<i class="fas fa-check"></i> Copied!';
            copyBtn.classList.add('copied');
            setTimeout(() => {
                copyBtn.innerHTML = '<i class="fas fa-copy"></i> Copy Result';
                copyBtn.classList.remove('copied');
            }, 2000);
        });
    });
    
    quickBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            fromValue.value = btn.getAttribute('data-value');
            convertUnits();
        });
    });
    
    // Initialize with default category
    populateUnits('length');
}

// ===== Currency Converter =====
function loadCurrencyConverter(container) {
    container.innerHTML = `
        <div class="converter-form">
            <div class="converter-row">
                <div class="converter-input-group">
                    <label class="converter-label">From</label>
                    <input type="number" class="converter-value" id="currencyFromValue" placeholder="Enter amount" value="1">
                    <select class="converter-unit" id="currencyFromUnit">
                        <option value="USD">USD - US Dollar</option>
                        <option value="EUR">EUR - Euro</option>
                        <option value="GBP">GBP - British Pound</option>
                        <option value="JPY">JPY - Japanese Yen</option>
                        <option value="CNY">CNY - Chinese Yuan</option>
                        <option value="CAD">CAD - Canadian Dollar</option>
                        <option value="AUD">AUD - Australian Dollar</option>
                        <option value="CHF">CHF - Swiss Franc</option>
                        <option value="SEK">SEK - Swedish Krona</option>
                        <option value="NOK">NOK - Norwegian Krone</option>
                    </select>
                </div>

                <button class="converter-swap" id="swapCurrency" title="Swap currencies">
                    <i class="fas fa-exchange-alt"></i>
                </button>

                <div class="converter-output-group">
                    <label class="converter-label">To</label>
                    <input type="number" class="converter-value" id="currencyToValue" placeholder="Result" readonly>
                    <select class="converter-unit" id="currencyToUnit">
                        <option value="USD">USD - US Dollar</option>
                        <option value="EUR">EUR - Euro</option>
                        <option value="GBP">GBP - British Pound</option>
                        <option value="JPY">JPY - Japanese Yen</option>
                        <option value="CNY">CNY - Chinese Yuan</option>
                        <option value="CAD">CAD - Canadian Dollar</option>
                        <option value="AUD">AUD - Australian Dollar</option>
                        <option value="CHF">CHF - Swiss Franc</option>
                        <option value="SEK">SEK - Swedish Krona</option>
                        <option value="NOK">NOK - Norwegian Krone</option>
                    </select>
                </div>
            </div>

            <div class="converter-result" id="currencyResult" style="display: none;">
                <div class="result-value" id="currencyResultValue">1.00</div>
                <div class="result-label" id="currencyResultLabel">1 USD = 0.85 EUR</div>
                <button class="copy-btn" id="copyCurrencyResult">
                    <i class="fas fa-copy"></i> Copy Result
                </button>
            </div>

            <div class="mt-3">
                <small class="text-muted">
                    <i class="fas fa-info-circle"></i>
                    Exchange rates are simulated for demo purposes. In a real application, this would use live API data.
                </small>
            </div>
        </div>
    `;

    initializeCurrencyConverter();
}

function initializeCurrencyConverter() {
    const fromValue = document.getElementById('currencyFromValue');
    const toValue = document.getElementById('currencyToValue');
    const fromUnit = document.getElementById('currencyFromUnit');
    const toUnit = document.getElementById('currencyToUnit');
    const swapBtn = document.getElementById('swapCurrency');
    const copyBtn = document.getElementById('copyCurrencyResult');

    // Simulated exchange rates (in a real app, this would come from an API)
    const exchangeRates = {
        USD: 1.0,
        EUR: 0.85,
        GBP: 0.73,
        JPY: 110.0,
        CNY: 6.45,
        CAD: 1.25,
        AUD: 1.35,
        CHF: 0.92,
        SEK: 8.5,
        NOK: 8.8
    };

    function convertCurrency() {
        const amount = parseFloat(fromValue.value) || 0;
        const fromCurrency = fromUnit.value;
        const toCurrency = toUnit.value;

        // Convert to USD first, then to target currency
        const usdAmount = amount / exchangeRates[fromCurrency];
        const result = usdAmount * exchangeRates[toCurrency];

        toValue.value = result.toFixed(2);

        // Update result display
        const resultDiv = document.getElementById('currencyResult');
        const resultValue = document.getElementById('currencyResultValue');
        const resultLabel = document.getElementById('currencyResultLabel');

        resultValue.textContent = result.toFixed(2);
        const rate = (exchangeRates[toCurrency] / exchangeRates[fromCurrency]).toFixed(4);
        resultLabel.textContent = `1 ${fromCurrency} = ${rate} ${toCurrency}`;
        resultDiv.style.display = 'block';
    }

    // Event listeners
    fromValue.addEventListener('input', convertCurrency);
    fromUnit.addEventListener('change', convertCurrency);
    toUnit.addEventListener('change', convertCurrency);

    swapBtn.addEventListener('click', () => {
        const tempUnit = fromUnit.value;
        fromUnit.value = toUnit.value;
        toUnit.value = tempUnit;
        convertCurrency();
    });

    copyBtn.addEventListener('click', () => {
        navigator.clipboard.writeText(toValue.value).then(() => {
            copyBtn.innerHTML = '<i class="fas fa-check"></i> Copied!';
            copyBtn.classList.add('copied');
            setTimeout(() => {
                copyBtn.innerHTML = '<i class="fas fa-copy"></i> Copy Result';
                copyBtn.classList.remove('copied');
            }, 2000);
        });
    });

    // Set default values
    toUnit.selectedIndex = 1; // EUR
    convertCurrency();
}

// ===== Color Converter =====
function loadColorConverter(container) {
    container.innerHTML = `
        <div class="converter-form">
            <div class="row">
                <div class="col-lg-6">
                    <div class="color-input-section">
                        <h5>Color Input</h5>
                        <div class="mb-3">
                            <label class="form-label">Color Picker</label>
                            <input type="color" class="form-control form-control-color" id="colorPicker" value="#3b82f6">
                        </div>

                        <div class="mb-3">
                            <label class="form-label">HEX</label>
                            <input type="text" class="form-control" id="hexInput" placeholder="#000000">
                        </div>

                        <div class="row">
                            <div class="col-4">
                                <label class="form-label">R</label>
                                <input type="number" class="form-control" id="rgbR" min="0" max="255">
                            </div>
                            <div class="col-4">
                                <label class="form-label">G</label>
                                <input type="number" class="form-control" id="rgbG" min="0" max="255">
                            </div>
                            <div class="col-4">
                                <label class="form-label">B</label>
                                <input type="number" class="form-control" id="rgbB" min="0" max="255">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="color-output-section">
                        <h5>Color Preview & Formats</h5>
                        <div class="color-preview" id="colorPreview"></div>

                        <div class="color-formats">
                            <div class="format-item">
                                <label>HEX:</label>
                                <span id="hexOutput">#3b82f6</span>
                                <button class="copy-format-btn" data-format="hex">Copy</button>
                            </div>
                            <div class="format-item">
                                <label>RGB:</label>
                                <span id="rgbOutput">rgb(59, 130, 246)</span>
                                <button class="copy-format-btn" data-format="rgb">Copy</button>
                            </div>
                            <div class="format-item">
                                <label>HSL:</label>
                                <span id="hslOutput">hsl(217, 91%, 60%)</span>
                                <button class="copy-format-btn" data-format="hsl">Copy</button>
                            </div>
                            <div class="format-item">
                                <label>CMYK:</label>
                                <span id="cmykOutput">cmyk(76%, 47%, 0%, 4%)</span>
                                <button class="copy-format-btn" data-format="cmyk">Copy</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    initializeColorConverter();
}

function initializeColorConverter() {
    const colorPicker = document.getElementById('colorPicker');
    const hexInput = document.getElementById('hexInput');
    const rgbR = document.getElementById('rgbR');
    const rgbG = document.getElementById('rgbG');
    const rgbB = document.getElementById('rgbB');
    const colorPreview = document.getElementById('colorPreview');
    const copyBtns = document.querySelectorAll('.copy-format-btn');

    function updateAllFormats(r, g, b) {
        // Update preview
        colorPreview.style.backgroundColor = `rgb(${r}, ${g}, ${b})`;

        // Update HEX
        const hex = '#' + [r, g, b].map(x => {
            const hex = x.toString(16);
            return hex.length === 1 ? '0' + hex : hex;
        }).join('');
        document.getElementById('hexOutput').textContent = hex;
        hexInput.value = hex;
        colorPicker.value = hex;

        // Update RGB
        document.getElementById('rgbOutput').textContent = `rgb(${r}, ${g}, ${b})`;
        rgbR.value = r;
        rgbG.value = g;
        rgbB.value = b;

        // Update HSL
        const hsl = rgbToHsl(r, g, b);
        document.getElementById('hslOutput').textContent = `hsl(${hsl.h}, ${hsl.s}%, ${hsl.l}%)`;

        // Update CMYK
        const cmyk = rgbToCmyk(r, g, b);
        document.getElementById('cmykOutput').textContent = `cmyk(${cmyk.c}%, ${cmyk.m}%, ${cmyk.y}%, ${cmyk.k}%)`;
    }

    function hexToRgb(hex) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : null;
    }

    function rgbToHsl(r, g, b) {
        r /= 255;
        g /= 255;
        b /= 255;

        const max = Math.max(r, g, b);
        const min = Math.min(r, g, b);
        let h, s, l = (max + min) / 2;

        if (max === min) {
            h = s = 0;
        } else {
            const d = max - min;
            s = l > 0.5 ? d / (2 - max - min) : d / (max + min);

            switch (max) {
                case r: h = (g - b) / d + (g < b ? 6 : 0); break;
                case g: h = (b - r) / d + 2; break;
                case b: h = (r - g) / d + 4; break;
            }
            h /= 6;
        }

        return {
            h: Math.round(h * 360),
            s: Math.round(s * 100),
            l: Math.round(l * 100)
        };
    }

    function rgbToCmyk(r, g, b) {
        r /= 255;
        g /= 255;
        b /= 255;

        const k = 1 - Math.max(r, Math.max(g, b));
        const c = (1 - r - k) / (1 - k) || 0;
        const m = (1 - g - k) / (1 - k) || 0;
        const y = (1 - b - k) / (1 - k) || 0;

        return {
            c: Math.round(c * 100),
            m: Math.round(m * 100),
            y: Math.round(y * 100),
            k: Math.round(k * 100)
        };
    }

    // Event listeners
    colorPicker.addEventListener('input', (e) => {
        const rgb = hexToRgb(e.target.value);
        if (rgb) updateAllFormats(rgb.r, rgb.g, rgb.b);
    });

    hexInput.addEventListener('input', (e) => {
        const rgb = hexToRgb(e.target.value);
        if (rgb) updateAllFormats(rgb.r, rgb.g, rgb.b);
    });

    [rgbR, rgbG, rgbB].forEach(input => {
        input.addEventListener('input', () => {
            const r = parseInt(rgbR.value) || 0;
            const g = parseInt(rgbG.value) || 0;
            const b = parseInt(rgbB.value) || 0;
            updateAllFormats(r, g, b);
        });
    });

    copyBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            const format = btn.getAttribute('data-format');
            const text = document.getElementById(format + 'Output').textContent;
            navigator.clipboard.writeText(text).then(() => {
                btn.textContent = 'Copied!';
                setTimeout(() => {
                    btn.textContent = 'Copy';
                }, 2000);
            });
        });
    });

    // Initialize with default color
    const defaultRgb = hexToRgb('#3b82f6');
    updateAllFormats(defaultRgb.r, defaultRgb.g, defaultRgb.b);
}

// ===== Encoding Converter =====
function loadEncodingConverter(container) {
    container.innerHTML = `
        <div class="converter-form">
            <div class="mb-4">
                <label class="form-label fw-semibold">Encoding Type</label>
                <select class="form-select" id="encodingType">
                    <option value="base64">Base64 Encode/Decode</option>
                    <option value="url">URL Encode/Decode</option>
                    <option value="html">HTML Entity Encode/Decode</option>
                    <option value="number">Number Base Converter</option>
                </select>
            </div>

            <div id="encodingContent">
                <!-- Content will be populated based on encoding type -->
            </div>
        </div>
    `;

    initializeEncodingConverter();
}

function initializeEncodingConverter() {
    const encodingType = document.getElementById('encodingType');
    const encodingContent = document.getElementById('encodingContent');

    function loadEncodingType(type) {
        switch(type) {
            case 'base64':
                loadBase64Converter();
                break;
            case 'url':
                loadUrlConverter();
                break;
            case 'html':
                loadHtmlConverter();
                break;
            case 'number':
                loadNumberConverter();
                break;
        }
    }

    function loadBase64Converter() {
        encodingContent.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <div class="converter-input-group">
                        <label class="converter-label">Plain Text</label>
                        <textarea class="form-control" id="base64Input" rows="6" placeholder="Enter text to encode..."></textarea>
                        <button class="btn btn-primary mt-2" id="encodeBase64">
                            <i class="fas fa-arrow-right"></i> Encode to Base64
                        </button>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="converter-output-group">
                        <label class="converter-label">Base64 Encoded</label>
                        <textarea class="form-control" id="base64Output" rows="6" placeholder="Base64 result..."></textarea>
                        <button class="btn btn-secondary mt-2" id="decodeBase64">
                            <i class="fas fa-arrow-left"></i> Decode from Base64
                        </button>
                    </div>
                </div>
            </div>
        `;

        const input = document.getElementById('base64Input');
        const output = document.getElementById('base64Output');
        const encodeBtn = document.getElementById('encodeBase64');
        const decodeBtn = document.getElementById('decodeBase64');

        encodeBtn.addEventListener('click', () => {
            try {
                output.value = btoa(unescape(encodeURIComponent(input.value)));
            } catch (e) {
                output.value = 'Error: Invalid input for encoding';
            }
        });

        decodeBtn.addEventListener('click', () => {
            try {
                input.value = decodeURIComponent(escape(atob(output.value)));
            } catch (e) {
                input.value = 'Error: Invalid Base64 string';
            }
        });
    }

    function loadUrlConverter() {
        encodingContent.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <div class="converter-input-group">
                        <label class="converter-label">Plain URL/Text</label>
                        <textarea class="form-control" id="urlInput" rows="6" placeholder="Enter URL or text to encode..."></textarea>
                        <button class="btn btn-primary mt-2" id="encodeUrl">
                            <i class="fas fa-arrow-right"></i> URL Encode
                        </button>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="converter-output-group">
                        <label class="converter-label">URL Encoded</label>
                        <textarea class="form-control" id="urlOutput" rows="6" placeholder="URL encoded result..."></textarea>
                        <button class="btn btn-secondary mt-2" id="decodeUrl">
                            <i class="fas fa-arrow-left"></i> URL Decode
                        </button>
                    </div>
                </div>
            </div>
        `;

        const input = document.getElementById('urlInput');
        const output = document.getElementById('urlOutput');
        const encodeBtn = document.getElementById('encodeUrl');
        const decodeBtn = document.getElementById('decodeUrl');

        encodeBtn.addEventListener('click', () => {
            output.value = encodeURIComponent(input.value);
        });

        decodeBtn.addEventListener('click', () => {
            try {
                input.value = decodeURIComponent(output.value);
            } catch (e) {
                input.value = 'Error: Invalid URL encoded string';
            }
        });
    }

    function loadNumberConverter() {
        encodingContent.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <div class="converter-input-group">
                        <label class="converter-label">Input Number</label>
                        <input type="text" class="form-control mb-2" id="numberInput" placeholder="Enter number...">
                        <select class="form-select" id="fromBase">
                            <option value="10">Decimal (Base 10)</option>
                            <option value="2">Binary (Base 2)</option>
                            <option value="8">Octal (Base 8)</option>
                            <option value="16">Hexadecimal (Base 16)</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="converter-output-group">
                        <label class="converter-label">Converted Results</label>
                        <div class="number-results">
                            <div class="result-item">
                                <label>Binary:</label>
                                <span id="binaryResult">0</span>
                            </div>
                            <div class="result-item">
                                <label>Octal:</label>
                                <span id="octalResult">0</span>
                            </div>
                            <div class="result-item">
                                <label>Decimal:</label>
                                <span id="decimalResult">0</span>
                            </div>
                            <div class="result-item">
                                <label>Hexadecimal:</label>
                                <span id="hexResult">0</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        const input = document.getElementById('numberInput');
        const fromBase = document.getElementById('fromBase');

        function convertNumber() {
            const value = input.value.trim();
            const base = parseInt(fromBase.value);

            if (!value) {
                document.getElementById('binaryResult').textContent = '0';
                document.getElementById('octalResult').textContent = '0';
                document.getElementById('decimalResult').textContent = '0';
                document.getElementById('hexResult').textContent = '0';
                return;
            }

            try {
                const decimal = parseInt(value, base);
                if (isNaN(decimal)) throw new Error('Invalid number');

                document.getElementById('binaryResult').textContent = decimal.toString(2);
                document.getElementById('octalResult').textContent = decimal.toString(8);
                document.getElementById('decimalResult').textContent = decimal.toString(10);
                document.getElementById('hexResult').textContent = decimal.toString(16).toUpperCase();
            } catch (e) {
                document.getElementById('binaryResult').textContent = 'Error';
                document.getElementById('octalResult').textContent = 'Error';
                document.getElementById('decimalResult').textContent = 'Error';
                document.getElementById('hexResult').textContent = 'Error';
            }
        }

        input.addEventListener('input', convertNumber);
        fromBase.addEventListener('change', convertNumber);
    }

    encodingType.addEventListener('change', () => {
        loadEncodingType(encodingType.value);
    });

    // Initialize with default type
    loadEncodingType('base64');
}



// ===== Timezone Converter =====
function loadTimezoneConverter(container) {
    container.innerHTML = `
        <div class="converter-form">
            <div class="row">
                <div class="col-md-6">
                    <div class="converter-input-group">
                        <label class="converter-label">From Location</label>
                        <input type="datetime-local" class="form-control mb-2" id="timezoneInput">
                        <select class="form-select" id="fromTimezone">
                            <optgroup label="UTC">
                                <option value="UTC">UTC - Coordinated Universal Time</option>
                            </optgroup>
                            <optgroup label="North America">
                                <option value="America/New_York">New York (EST/EDT)</option>
                                <option value="America/Chicago">Chicago (CST/CDT)</option>
                                <option value="America/Denver">Denver (MST/MDT)</option>
                                <option value="America/Los_Angeles">Los Angeles (PST/PDT)</option>
                                <option value="America/Toronto">Toronto (EST/EDT)</option>
                                <option value="America/Vancouver">Vancouver (PST/PDT)</option>
                                <option value="America/Mexico_City">Mexico City (CST/CDT)</option>
                            </optgroup>
                            <optgroup label="Europe">
                                <option value="Europe/London">London (GMT/BST)</option>
                                <option value="Europe/Paris">Paris (CET/CEST)</option>
                                <option value="Europe/Berlin">Berlin (CET/CEST)</option>
                                <option value="Europe/Rome">Rome (CET/CEST)</option>
                                <option value="Europe/Madrid">Madrid (CET/CEST)</option>
                                <option value="Europe/Amsterdam">Amsterdam (CET/CEST)</option>
                                <option value="Europe/Stockholm">Stockholm (CET/CEST)</option>
                                <option value="Europe/Moscow">Moscow (MSK)</option>
                            </optgroup>
                            <optgroup label="Asia">
                                <option value="Asia/Tokyo">Tokyo (JST)</option>
                                <option value="Asia/Shanghai">Shanghai (CST)</option>
                                <option value="Asia/Hong_Kong">Hong Kong (HKT)</option>
                                <option value="Asia/Singapore">Singapore (SGT)</option>
                                <option value="Asia/Seoul">Seoul (KST)</option>
                                <option value="Asia/Mumbai">Mumbai (IST)</option>
                                <option value="Asia/Dubai">Dubai (GST)</option>
                                <option value="Asia/Bangkok">Bangkok (ICT)</option>
                            </optgroup>
                            <optgroup label="Oceania">
                                <option value="Australia/Sydney">Sydney (AEST/AEDT)</option>
                                <option value="Australia/Melbourne">Melbourne (AEST/AEDT)</option>
                                <option value="Australia/Perth">Perth (AWST)</option>
                                <option value="Pacific/Auckland">Auckland (NZST/NZDT)</option>
                            </optgroup>
                            <optgroup label="Africa">
                                <option value="Africa/Cairo">Cairo (EET)</option>
                                <option value="Africa/Lagos">Lagos (WAT)</option>
                                <option value="Africa/Johannesburg">Johannesburg (SAST)</option>
                            </optgroup>
                            <optgroup label="South America">
                                <option value="America/Sao_Paulo">São Paulo (BRT)</option>
                                <option value="America/Buenos_Aires">Buenos Aires (ART)</option>
                                <option value="America/Lima">Lima (PET)</option>
                            </optgroup>
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="converter-output-group">
                        <label class="converter-label">To Location</label>
                        <input type="text" class="form-control mb-2" id="timezoneOutput" readonly>
                        <select class="form-select" id="toTimezone">
                            <optgroup label="UTC">
                                <option value="UTC">UTC - Coordinated Universal Time</option>
                            </optgroup>
                            <optgroup label="North America">
                                <option value="America/New_York">New York (EST/EDT)</option>
                                <option value="America/Chicago">Chicago (CST/CDT)</option>
                                <option value="America/Denver">Denver (MST/MDT)</option>
                                <option value="America/Los_Angeles">Los Angeles (PST/PDT)</option>
                                <option value="America/Toronto">Toronto (EST/EDT)</option>
                                <option value="America/Vancouver">Vancouver (PST/PDT)</option>
                                <option value="America/Mexico_City">Mexico City (CST/CDT)</option>
                            </optgroup>
                            <optgroup label="Europe">
                                <option value="Europe/London">London (GMT/BST)</option>
                                <option value="Europe/Paris">Paris (CET/CEST)</option>
                                <option value="Europe/Berlin">Berlin (CET/CEST)</option>
                                <option value="Europe/Rome">Rome (CET/CEST)</option>
                                <option value="Europe/Madrid">Madrid (CET/CEST)</option>
                                <option value="Europe/Amsterdam">Amsterdam (CET/CEST)</option>
                                <option value="Europe/Stockholm">Stockholm (CET/CEST)</option>
                                <option value="Europe/Moscow">Moscow (MSK)</option>
                            </optgroup>
                            <optgroup label="Asia">
                                <option value="Asia/Tokyo">Tokyo (JST)</option>
                                <option value="Asia/Shanghai">Shanghai (CST)</option>
                                <option value="Asia/Hong_Kong">Hong Kong (HKT)</option>
                                <option value="Asia/Singapore">Singapore (SGT)</option>
                                <option value="Asia/Seoul">Seoul (KST)</option>
                                <option value="Asia/Mumbai">Mumbai (IST)</option>
                                <option value="Asia/Dubai">Dubai (GST)</option>
                                <option value="Asia/Bangkok">Bangkok (ICT)</option>
                            </optgroup>
                            <optgroup label="Oceania">
                                <option value="Australia/Sydney">Sydney (AEST/AEDT)</option>
                                <option value="Australia/Melbourne">Melbourne (AEST/AEDT)</option>
                                <option value="Australia/Perth">Perth (AWST)</option>
                                <option value="Pacific/Auckland">Auckland (NZST/NZDT)</option>
                            </optgroup>
                            <optgroup label="Africa">
                                <option value="Africa/Cairo">Cairo (EET)</option>
                                <option value="Africa/Lagos">Lagos (WAT)</option>
                                <option value="Africa/Johannesburg">Johannesburg (SAST)</option>
                            </optgroup>
                            <optgroup label="South America">
                                <option value="America/Sao_Paulo">São Paulo (BRT)</option>
                                <option value="America/Buenos_Aires">Buenos Aires (ART)</option>
                                <option value="America/Lima">Lima (PET)</option>
                            </optgroup>
                        </select>
                    </div>
                </div>
            </div>

            <div class="timezone-tools mt-4">
                <div class="row">
                    <div class="col-md-6">
                        <div class="meeting-planner">
                            <h5><i class="fas fa-calendar-alt"></i> Meeting Planner</h5>
                            <p class="text-muted">Find the best time for international meetings</p>
                            <button class="btn btn-outline-primary btn-sm" id="addTimezone">
                                <i class="fas fa-plus"></i> Add Timezone
                            </button>
                            <div id="meetingTimezones" class="mt-3">
                                <!-- Meeting timezone items will be added here -->
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="quick-times">
                            <h5><i class="fas fa-clock"></i> Quick Times</h5>
                            <div class="quick-time-buttons">
                                <button class="btn btn-outline-secondary btn-sm quick-time-btn" data-time="09:00">9:00 AM</button>
                                <button class="btn btn-outline-secondary btn-sm quick-time-btn" data-time="12:00">12:00 PM</button>
                                <button class="btn btn-outline-secondary btn-sm quick-time-btn" data-time="15:00">3:00 PM</button>
                                <button class="btn btn-outline-secondary btn-sm quick-time-btn" data-time="18:00">6:00 PM</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="world-clock mt-4">
                <h5><i class="fas fa-globe"></i> World Clock</h5>
                <div class="row" id="worldClockContainer">
                    <!-- World clock items will be populated here -->
                </div>
            </div>
        </div>
    `;

    initializeTimezoneConverter();
}

function initializeTimezoneConverter() {
    const timezoneInput = document.getElementById('timezoneInput');
    const timezoneOutput = document.getElementById('timezoneOutput');
    const fromTimezone = document.getElementById('fromTimezone');
    const toTimezone = document.getElementById('toTimezone');
    const worldClockContainer = document.getElementById('worldClockContainer');
    const addTimezoneBtn = document.getElementById('addTimezone');
    const meetingTimezones = document.getElementById('meetingTimezones');
    const quickTimeBtns = document.querySelectorAll('.quick-time-btn');

    // Set current time as default
    const now = new Date();
    timezoneInput.value = now.toISOString().slice(0, 16);

    // Extended timezone offsets (simplified for demo)
    const timezoneOffsets = {
        'UTC': 0,
        'America/New_York': -5, 'America/Chicago': -6, 'America/Denver': -7, 'America/Los_Angeles': -8,
        'America/Toronto': -5, 'America/Vancouver': -8, 'America/Mexico_City': -6,
        'Europe/London': 0, 'Europe/Paris': 1, 'Europe/Berlin': 1, 'Europe/Rome': 1,
        'Europe/Madrid': 1, 'Europe/Amsterdam': 1, 'Europe/Stockholm': 1, 'Europe/Moscow': 3,
        'Asia/Tokyo': 9, 'Asia/Shanghai': 8, 'Asia/Hong_Kong': 8, 'Asia/Singapore': 8,
        'Asia/Seoul': 9, 'Asia/Mumbai': 5.5, 'Asia/Dubai': 4, 'Asia/Bangkok': 7,
        'Australia/Sydney': 11, 'Australia/Melbourne': 11, 'Australia/Perth': 8, 'Pacific/Auckland': 13,
        'Africa/Cairo': 2, 'Africa/Lagos': 1, 'Africa/Johannesburg': 2,
        'America/Sao_Paulo': -3, 'America/Buenos_Aires': -3, 'America/Lima': -5
    };

    let meetingTimezoneList = ['America/New_York', 'Europe/London', 'Asia/Tokyo'];

    function convertTimezone() {
        const inputTime = new Date(timezoneInput.value);
        if (isNaN(inputTime)) return;

        const fromOffset = timezoneOffsets[fromTimezone.value] || 0;
        const toOffset = timezoneOffsets[toTimezone.value] || 0;

        const utcTime = new Date(inputTime.getTime() - (fromOffset * 60 * 60 * 1000));
        const convertedTime = new Date(utcTime.getTime() + (toOffset * 60 * 60 * 1000));

        timezoneOutput.value = convertedTime.toLocaleString('en-US', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        });
    }

    function updateWorldClock() {
        const majorTimezones = [
            { name: 'New York', timezone: 'America/New_York', flag: '🇺🇸' },
            { name: 'London', timezone: 'Europe/London', flag: '🇬🇧' },
            { name: 'Paris', timezone: 'Europe/Paris', flag: '🇫🇷' },
            { name: 'Tokyo', timezone: 'Asia/Tokyo', flag: '🇯🇵' },
            { name: 'Sydney', timezone: 'Australia/Sydney', flag: '🇦🇺' },
            { name: 'Dubai', timezone: 'Asia/Dubai', flag: '🇦🇪' }
        ];

        const now = new Date();

        worldClockContainer.innerHTML = majorTimezones.map(tz => {
            const offset = timezoneOffsets[tz.timezone] || 0;
            const localTime = new Date(now.getTime() + (offset * 60 * 60 * 1000));
            const timeString = localTime.toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: true
            });
            const dateString = localTime.toLocaleDateString('en-US', {
                month: 'short',
                day: 'numeric'
            });

            return `
                <div class="col-lg-4 col-md-6 mb-3">
                    <div class="world-clock-item">
                        <div class="clock-header">
                            <span class="flag">${tz.flag}</span>
                            <h6>${tz.name}</h6>
                        </div>
                        <div class="time">${timeString}</div>
                        <small class="text-muted">${dateString}</small>
                    </div>
                </div>
            `;
        }).join('');
    }

    function updateMeetingPlanner() {
        const baseTime = new Date(timezoneInput.value);
        if (isNaN(baseTime)) return;

        meetingTimezones.innerHTML = meetingTimezoneList.map((tz, index) => {
            const offset = timezoneOffsets[tz] || 0;
            const fromOffset = timezoneOffsets[fromTimezone.value] || 0;

            const utcTime = new Date(baseTime.getTime() - (fromOffset * 60 * 60 * 1000));
            const localTime = new Date(utcTime.getTime() + (offset * 60 * 60 * 1000));

            const cityName = tz.split('/')[1].replace('_', ' ');
            const timeString = localTime.toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: true
            });

            return `
                <div class="meeting-timezone-item">
                    <div class="meeting-info">
                        <strong>${cityName}</strong>
                        <span class="meeting-time">${timeString}</span>
                    </div>
                    <button class="btn btn-sm btn-outline-danger remove-timezone" data-index="${index}">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
        }).join('');

        // Add event listeners for remove buttons
        document.querySelectorAll('.remove-timezone').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const index = parseInt(e.target.closest('.remove-timezone').dataset.index);
                meetingTimezoneList.splice(index, 1);
                updateMeetingPlanner();
            });
        });
    }

    // Event listeners
    timezoneInput.addEventListener('change', () => {
        convertTimezone();
        updateMeetingPlanner();
    });
    fromTimezone.addEventListener('change', () => {
        convertTimezone();
        updateMeetingPlanner();
    });
    toTimezone.addEventListener('change', convertTimezone);

    addTimezoneBtn.addEventListener('click', () => {
        const selectedTimezone = fromTimezone.value;
        if (!meetingTimezoneList.includes(selectedTimezone)) {
            meetingTimezoneList.push(selectedTimezone);
            updateMeetingPlanner();
        }
    });

    quickTimeBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            const time = btn.dataset.time;
            const today = new Date();
            const dateStr = today.toISOString().split('T')[0];
            timezoneInput.value = `${dateStr}T${time}`;
            convertTimezone();
            updateMeetingPlanner();
        });
    });

    // Set default target timezone
    toTimezone.selectedIndex = 1;

    // Initialize
    convertTimezone();
    updateWorldClock();
    updateMeetingPlanner();

    // Update world clock every second
    setInterval(updateWorldClock, 1000);
}

// ===== Hero Demo Animation =====
function initializeHeroDemo() {
    const demoValue = document.getElementById('demoValue');
    const demoFromUnit = document.getElementById('demoFromUnit');
    const demoResult = document.getElementById('demoResult');
    const demoToUnit = document.getElementById('demoToUnit');

    if (!demoValue) return;

    const conversions = [
        {
            value: 100,
            from: 'meters',
            result: '328.08',
            to: 'feet',
            icon: 'fas fa-ruler',
            type: 'Length'
        },
        {
            value: 50,
            from: 'kg',
            result: '110.23',
            to: 'pounds',
            icon: 'fas fa-weight',
            type: 'Weight'
        },
        {
            value: 25,
            from: '°C',
            result: '77',
            to: '°F',
            icon: 'fas fa-thermometer-half',
            type: 'Temperature'
        },
        {
            value: 1,
            from: 'USD',
            result: '0.85',
            to: 'EUR',
            icon: 'fas fa-coins',
            type: 'Currency'
        },
        {
            value: 1,
            from: 'liter',
            result: '0.26',
            to: 'gallon',
            icon: 'fas fa-flask',
            type: 'Volume'
        }
    ];

    let currentIndex = 0;

    function updateDemo() {
        const conversion = conversions[currentIndex];
        const inputType = document.querySelector('.input-type');
        const outputType = document.querySelector('.output-type');

        // Animate the change
        const elements = [demoValue, demoFromUnit, demoResult, demoToUnit];
        elements.forEach(el => {
            el.style.transition = 'opacity 0.3s ease';
            el.style.opacity = '0.5';
        });

        setTimeout(() => {
            demoValue.textContent = conversion.value;
            demoFromUnit.textContent = conversion.from;
            demoResult.textContent = conversion.result;
            demoToUnit.textContent = conversion.to;

            // Update type indicators if they exist
            if (inputType && outputType) {
                inputType.innerHTML = `<i class="${conversion.icon}"></i><span>${conversion.type}</span>`;
                outputType.innerHTML = `<i class="${conversion.icon}"></i><span>${conversion.type}</span>`;
            }

            elements.forEach(el => el.style.opacity = '1');
        }, 300);

        currentIndex = (currentIndex + 1) % conversions.length;
    }

    // Start demo and update every 4 seconds
    updateDemo();
    setInterval(updateDemo, 4000);

    // Add click handlers to floating cards
    const floatingCards = document.querySelectorAll('.floating-card');
    floatingCards.forEach((card, index) => {
        card.addEventListener('click', () => {
            const converterNames = ['units', 'currency', 'colors', 'encoding', 'timezone'];
            const targetConverter = converterNames[index];
            if (targetConverter) {
                // Scroll to converters section
                const convertersSection = document.getElementById('converters');
                if (convertersSection) {
                    convertersSection.scrollIntoView({ behavior: 'smooth' });

                    // Activate the corresponding tab after a short delay
                    setTimeout(() => {
                        const tabButton = document.querySelector(`#${targetConverter}-tab`);
                        if (tabButton) {
                            tabButton.click();
                        }
                    }, 800);
                }
            }
        });
    });

    // Add click handlers to new tool cards
    const toolCards = document.querySelectorAll('.tool-card');
    toolCards.forEach(card => {
        card.addEventListener('click', function() {
            const tool = this.dataset.tool;
            if (tool) {
                // Scroll to the corresponding converter section
                const converterSection = document.getElementById(tool);
                if (converterSection) {
                    converterSection.scrollIntoView({ behavior: 'smooth' });
                } else {
                    // If section doesn't exist, scroll to converters
                    const convertersSection = document.getElementById('converters');
                    if (convertersSection) {
                        convertersSection.scrollIntoView({ behavior: 'smooth' });

                        // Try to activate the corresponding tab
                        setTimeout(() => {
                            const tabButton = document.querySelector(`#${tool}-tab`);
                            if (tabButton) {
                                tabButton.click();
                            }
                        }, 500);
                    }
                }
            }
        });

        // Add enhanced hover effects
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
}

// ===== Scroll Animations =====
function initializeScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';

                // Add stagger effect for child elements
                const children = entry.target.querySelectorAll('.floating-card, .converter-container');
                children.forEach((child, index) => {
                    setTimeout(() => {
                        child.style.opacity = '1';
                        child.style.transform = 'translateY(0)';
                    }, index * 100);
                });
            }
        });
    }, observerOptions);

    // Observe elements that should animate on scroll
    const animatedElements = document.querySelectorAll('.converters-section');
    animatedElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        observer.observe(el);
    });

    // Observe child elements
    const childElements = document.querySelectorAll('.floating-card, .converter-container');
    childElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(20px)';
    });
}

// ===== Enhanced User Interactions =====
function initializeEnhancedInteractions() {
    // Add ripple effect to buttons
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');

            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });

    // Add hover effects to converter tabs
    const converterTabs = document.querySelectorAll('.converter-tabs .nav-link');
    converterTabs.forEach(tab => {
        tab.addEventListener('mouseenter', function() {
            if (!this.classList.contains('active')) {
                this.style.transform = 'translateY(-2px)';
            }
        });

        tab.addEventListener('mouseleave', function() {
            if (!this.classList.contains('active')) {
                this.style.transform = 'translateY(0)';
            }
        });
    });

    // Add keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + K to focus search (if implemented)
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            // Focus search input if available
        }

        // Escape to clear inputs
        if (e.key === 'Escape') {
            const activeInputs = document.querySelectorAll('input:focus, textarea:focus');
            activeInputs.forEach(input => {
                if (input.type !== 'datetime-local' && input.type !== 'color') {
                    input.value = '';
                    input.dispatchEvent(new Event('input'));
                }
            });
        }
    });
}

// ===== Utility Functions =====
function formatNumber(num, decimals = 2) {
    return parseFloat(num).toFixed(decimals).replace(/\.?0+$/, '');
}

function copyToClipboard(text) {
    return navigator.clipboard.writeText(text);
}

function showNotification(message, type = 'info') {
    // Simple notification system
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fas fa-${type === 'success' ? 'check' : 'info'}-circle me-2"></i>
            ${message}
        </div>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// Initialize enhanced interactions when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        initializeEnhancedInteractions();
    }, 500);
});
