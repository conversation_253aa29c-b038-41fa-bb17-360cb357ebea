/* ===== CSS Variables ===== */
:root {
    --primary-color: #7c3aed;
    --primary-dark: #6d28d9;
    --primary-light: #a855f7;
    --secondary-color: #64748b;
    --accent-color: #f59e0b;
    --accent-secondary: #06b6d4;
    --success-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    --info-color: #06b6d4;

    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;

    --bg-primary: #ffffff;
    --bg-secondary: #faf7ff;
    --bg-tertiary: #f3f0ff;
    --bg-gradient: linear-gradient(135deg, #7c3aed 0%, #a855f7 50%, #06b6d4 100%);
    --bg-gradient-light: linear-gradient(135deg, #faf7ff 0%, #f3f0ff 100%);
    --gradient-primary: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);

    --border-color: #e2e8f0;
    --border-light: #f1f5f9;
    --border-purple: #e9d5ff;

    --shadow-sm: 0 1px 2px 0 rgb(124 58 237 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(124 58 237 / 0.1), 0 2px 4px -2px rgb(124 58 237 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(124 58 237 / 0.1), 0 4px 6px -4px rgb(124 58 237 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(124 58 237 / 0.1), 0 8px 10px -6px rgb(124 58 237 / 0.1);
    --shadow-purple: 0 10px 25px -5px rgb(124 58 237 / 0.2);

    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;

    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* ===== Base Styles ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    overflow-x: hidden;
    scroll-behavior: smooth;
}

.container {
    max-width: 1200px;
}

/* Loading Animation */
.loading {
    opacity: 0;
    animation: fadeIn 0.6s ease-in-out forwards;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Smooth Transitions */
* {
    transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease, transform 0.3s ease;
}

/* ===== Header Styles ===== */
.header {
    background: var(--bg-primary);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 1000;
    transition: all 0.3s ease;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--primary-color) !important;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.navbar-brand i {
    font-size: 1.75rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    color: var(--text-secondary) !important;
    padding: 0.75rem 1rem !important;
    transition: all 0.3s ease;
    position: relative;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
}

.navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.navbar-nav .nav-link:hover::after {
    width: 80%;
}

/* ===== Mobile Menu Styles ===== */
.navbar-toggler {
    border: none;
    padding: 0.25rem 0.5rem;
    font-size: 1.25rem;
    line-height: 1;
    background-color: transparent;
    border-radius: var(--radius-md);
    transition: all 0.3s ease;
}

.navbar-toggler:focus {
    box-shadow: none;
}

.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%2833, 37, 41, 0.75%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
    width: 1.5em;
    height: 1.5em;
}

/* Mobile Menu Close Button */
.mobile-menu-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: none;
    border: none;
    font-size: 2rem;
    color: var(--text-primary);
    cursor: pointer;
    z-index: 1001;
    width: 3rem;
    height: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.mobile-menu-close:hover {
    background-color: rgba(0, 0, 0, 0.1);
    transform: rotate(90deg);
}

/* Mobile Menu Overlay */
@media (max-width: 991.98px) {
    .navbar-collapse {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(10px);
        z-index: 1000;
        display: flex !important;
        align-items: center;
        justify-content: center;
        opacity: 0;
        visibility: hidden;
        transform: scale(0.9);
        transition: all 0.3s ease;
    }

    .navbar-collapse.show {
        opacity: 1;
        visibility: visible;
        transform: scale(1);
    }

    .navbar-nav {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
        text-align: center;
    }

    .navbar-nav .nav-item {
        margin: 0;
    }

    .navbar-nav .nav-link {
        font-size: 1.5rem !important;
        font-weight: 600 !important;
        padding: 1rem 2rem !important;
        color: var(--text-primary) !important;
        border-radius: var(--radius-lg);
        transition: all 0.3s ease;
        min-width: 200px;
        text-align: center;
    }

    .navbar-nav .nav-link:hover {
        background-color: var(--bg-secondary);
        color: var(--primary-color) !important;
        transform: translateY(-2px);
    }

    .navbar-nav .nav-link::after {
        display: none;
    }
}

/* ===== Hero Section ===== */
.hero-section {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 8rem 0;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(124, 58, 237, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(16, 185, 129, 0.06) 0%, transparent 50%);
    pointer-events: none;
}

.hero-section::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(45deg, transparent 30%, rgba(124, 58, 237, 0.02) 50%, transparent 70%),
        linear-gradient(-45deg, transparent 30%, rgba(59, 130, 246, 0.02) 50%, transparent 70%);
    pointer-events: none;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.hero-description {
    font-size: 1.25rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    max-width: 500px;
}

.hero-features {
    display: flex;
    gap: 1rem;
    margin-bottom: 2.5rem;
    flex-wrap: wrap;
}

.feature-badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(124, 58, 237, 0.1);
    color: var(--primary-color);
    padding: 0.75rem 1rem;
    border-radius: var(--radius-xl);
    border: 1px solid rgba(124, 58, 237, 0.2);
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.feature-badge:hover {
    background: rgba(124, 58, 237, 0.15);
    transform: translateY(-2px);
}

.feature-badge i {
    font-size: 1rem;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn {
    padding: 0.75rem 2rem;
    font-weight: 600;
    border-radius: var(--radius-lg);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

/* Ripple Effect */
.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

.btn-primary {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background: var(--primary-dark);
    border-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-outline-primary {
    background: transparent;
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* ===== Hero Visual ===== */
.hero-visual {
    position: relative;
    padding: 2rem 0;
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

/* Conversion Demo */
.conversion-demo {
    position: relative;
    z-index: 10;
}

.conversion-card {
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    border-radius: var(--radius-2xl);
    padding: 2.5rem;
    box-shadow: var(--shadow-purple);
    display: flex;
    align-items: center;
    gap: 2rem;
    min-width: 320px;
    border: 3px solid transparent;
    background-clip: padding-box;
    position: relative;
}

.conversion-card::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    background: var(--bg-gradient);
    border-radius: var(--radius-2xl);
    z-index: -1;
}

.conversion-input,
.conversion-output {
    text-align: center;
    flex: 1;
}

.conversion-input .value,
.conversion-output .value {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
}

.conversion-input .unit,
.conversion-output .unit {
    font-size: 0.875rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.conversion-arrow {
    color: var(--primary-color);
    font-size: 1.5rem;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* Floating Cards */
.floating-card {
    position: absolute;
    background: var(--bg-primary);
    padding: 1rem 1.5rem;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    text-align: center;
    min-width: 120px;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    cursor: pointer;
}

.floating-card:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.floating-card.card-1 {
    top: 10%;
    left: 5%;
    animation: float 6s ease-in-out infinite;
    animation-delay: 0s;
}

.floating-card.card-2 {
    top: 15%;
    right: 8%;
    animation: float 6s ease-in-out infinite;
    animation-delay: 1.2s;
}

.floating-card.card-3 {
    bottom: 25%;
    left: 2%;
    animation: float 6s ease-in-out infinite;
    animation-delay: 2.4s;
}

.floating-card.card-4 {
    bottom: 15%;
    right: 5%;
    animation: float 6s ease-in-out infinite;
    animation-delay: 3.6s;
}

.floating-card.card-5 {
    top: 45%;
    left: -5%;
    animation: float 6s ease-in-out infinite;
    animation-delay: 4.8s;
}

.floating-card i {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    display: block;
}

.floating-card h4 {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: var(--text-primary);
}

.floating-card p {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin: 0;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-15px); }
}

/* ===== Hero Visual Elements (New) ===== */
.hero-visual .conversion-showcase {
    margin-bottom: 3rem;
}

.hero-visual .conversion-demo-card {
    background: var(--bg-primary);
    border-radius: var(--radius-2xl);
    padding: 2rem;
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
}

.hero-visual .conversion-demo-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
}

.hero-visual .demo-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
    color: var(--primary-color);
    font-weight: 600;
}

.hero-visual .demo-header i {
    font-size: 1.25rem;
}

.hero-visual .conversion-example {
    display: flex;
    align-items: center;
    gap: 2rem;
    justify-content: space-between;
}

.hero-visual .conversion-example .conversion-input,
.hero-visual .conversion-example .conversion-output {
    flex: 1;
    text-align: center;
    padding: 1.5rem;
    background: var(--bg-secondary);
    border-radius: var(--radius-xl);
    border: 2px solid var(--border-color);
    transition: all 0.3s ease;
}

.hero-visual .conversion-example .conversion-input:hover,
.hero-visual .conversion-example .conversion-output:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.hero-visual .conversion-example .conversion-input .value,
.hero-visual .conversion-example .conversion-output .value {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    line-height: 1;
    margin-bottom: 0.5rem;
}

.hero-visual .conversion-example .conversion-input .unit,
.hero-visual .conversion-example .conversion-output .unit {
    font-size: 1.125rem;
    color: var(--text-secondary);
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.hero-visual .input-type,
.hero-visual .output-type {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    color: var(--text-muted);
    font-size: 0.875rem;
}

.hero-visual .conversion-example .conversion-arrow {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.hero-visual .arrow-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    animation: pulse-new 2s infinite;
    box-shadow: var(--shadow-lg);
    position: relative;
}

/* 确保FontAwesome图标正确显示 */
.hero-visual .arrow-icon i {
    font-family: "Font Awesome 6 Free", "Font Awesome 5 Free", "FontAwesome";
    font-weight: 900;
    font-style: normal;
    display: inline-block;
    line-height: 1;
}

/* 如果FontAwesome没有加载，显示备用箭头 */
.hero-visual .arrow-icon::before {
    content: "→";
    font-size: 1.5rem;
    font-weight: bold;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: -1;
}

.hero-visual .arrow-icon i:not(:empty) + ::before {
    display: none;
}

.hero-visual .conversion-type {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
}

@keyframes pulse-new {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.hero-visual .tools-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 1rem;
}

.hero-visual .tool-card {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    padding: 1.5rem;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.hero-visual .tool-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.hero-visual .tool-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.hero-visual .tool-card:hover::before {
    transform: scaleX(1);
}

.hero-visual .tool-icon {
    width: 60px;
    height: 60px;
    background: rgba(124, 58, 237, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    transition: all 0.3s ease;
}

.hero-visual .tool-card:hover .tool-icon {
    background: var(--primary-color);
    transform: scale(1.1);
}

.hero-visual .tool-icon i {
    font-size: 1.5rem;
    color: var(--primary-color);
    transition: color 0.3s ease;
}

.hero-visual .tool-card:hover .tool-icon i {
    color: white;
}

.hero-visual .tool-card h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.hero-visual .tool-card p {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: 1rem;
    line-height: 1.4;
}

.hero-visual .tool-examples {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

.hero-visual .tool-examples span {
    background: var(--bg-secondary);
    color: var(--text-secondary);
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius-md);
    font-size: 0.75rem;
    font-weight: 500;
    border: 1px solid var(--border-color);
}

.hero-visual .color-example {
    color: white !important;
    font-weight: 600 !important;
}

/* ===== Converters Section ===== */
.converters-section {
    padding: 6rem 0;
    background: var(--bg-primary);
    position: relative;
}

.converters-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 20% 20%, rgba(124, 58, 237, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(245, 158, 11, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 40% 60%, rgba(16, 185, 129, 0.05) 0%, transparent 50%);
    pointer-events: none;
}

.section-header {
    margin-bottom: 4rem;
}

.section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.section-header p {
    font-size: 1.125rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* ===== Converter Tabs ===== */
.converter-tabs {
    margin-bottom: 3rem;
}

.converter-tabs .nav-pills {
    background: var(--bg-secondary);
    padding: 0.5rem;
    border-radius: var(--radius-xl);
    gap: 0.25rem;
    flex-wrap: wrap;
}

.converter-tabs .nav-link {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    font-weight: 500;
    padding: 1rem 1.5rem;
    border-radius: var(--radius-lg);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    white-space: nowrap;
    min-width: 140px;
    justify-content: center;
}

.converter-tabs .nav-link:hover {
    background: var(--bg-primary);
    color: var(--text-primary);
    box-shadow: var(--shadow-sm);
    transform: translateY(-2px);
}

.converter-tabs .nav-link.active {
    background: var(--primary-color);
    color: white;
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.converter-tabs .nav-link.active i {
    animation: pulse 2s infinite;
}

.converter-tabs .nav-link i {
    font-size: 1.125rem;
}

/* ===== Converter Container ===== */
.converter-container {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    position: relative;
    z-index: 2;
}

.converter-header {
    background: var(--bg-secondary);
    padding: 2rem;
    text-align: center;
    border-bottom: 1px solid var(--border-color);
}

.converter-header h3 {
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.converter-header p {
    color: var(--text-secondary);
    margin: 0;
}

.converter-content {
    padding: 2rem;
    min-height: 400px;
}

/* ===== Converter Form Styles ===== */
.converter-form {
    max-width: 800px;
    margin: 0 auto;
}

.converter-row {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 2rem;
    align-items: center;
    margin-bottom: 2rem;
}

.converter-input-group,
.converter-output-group {
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    border: 2px solid var(--border-color);
    transition: all 0.3s ease;
}

.converter-input-group:focus-within,
.converter-output-group:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(124, 58, 237, 0.25);
}

.converter-label {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.75rem;
    display: block;
}

.converter-value {
    width: 100%;
    border: none;
    background: transparent;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.75rem;
    outline: none;
}

.converter-value:focus {
    color: var(--primary-color);
}

.converter-unit {
    width: 100%;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: 0.5rem;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-weight: 500;
}

.converter-unit:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(124, 58, 237, 0.25);
    outline: none;
}

.converter-swap {
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.converter-swap:hover {
    background: var(--primary-dark);
    transform: rotate(180deg) scale(1.1);
    box-shadow: var(--shadow-lg);
}

/* ===== Result Display ===== */
.converter-result {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    padding: 1.5rem;
    border-radius: var(--radius-lg);
    text-align: center;
    margin-top: 2rem;
}

.result-value {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.result-label {
    font-size: 0.875rem;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* ===== Copy Button ===== */
.copy-btn {
    background: var(--success-color);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 1rem;
}

.copy-btn:hover {
    background: #059669;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.copy-btn.copied {
    background: var(--info-color);
}

/* ===== Quick Convert Buttons ===== */
.quick-convert {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin-top: 1rem;
}

.quick-btn {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
}

.quick-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

/* ===== Color Converter Specific Styles ===== */
.color-preview {
    width: 100%;
    height: 100px;
    border-radius: var(--radius-lg);
    border: 2px solid var(--border-color);
    margin-bottom: 1rem;
    background: #3b82f6;
    transition: all 0.3s ease;
}

.color-formats {
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    padding: 1rem;
}

.format-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-light);
}

.format-item:last-child {
    border-bottom: none;
}

.format-item label {
    font-weight: 600;
    color: var(--text-primary);
    min-width: 60px;
}

.format-item span {
    font-family: 'Courier New', monospace;
    color: var(--text-secondary);
    flex: 1;
    margin: 0 1rem;
}

.copy-format-btn {
    background: var(--info-color);
    color: white;
    border: none;
    border-radius: var(--radius-sm);
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.copy-format-btn:hover {
    background: #0891b2;
    transform: translateY(-1px);
}

/* ===== Encoding Converter Styles ===== */
.number-results {
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    padding: 1rem;
}

.result-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border-light);
}

.result-item:last-child {
    border-bottom: none;
}

.result-item label {
    font-weight: 600;
    color: var(--text-primary);
    min-width: 100px;
}

.result-item span {
    font-family: 'Courier New', monospace;
    color: var(--primary-color);
    font-weight: 600;
    font-size: 1.1rem;
}

/* ===== File Converter Placeholder Styles ===== */
.file-converter-placeholder {
    padding: 3rem 2rem;
}

.supported-formats {
    margin-top: 2rem;
}

.format-badges {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    justify-content: center;
    margin-top: 1rem;
}

.format-badges .badge {
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
}

/* ===== Timezone Converter Styles ===== */
.world-clock {
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    margin-top: 2rem;
}

.world-clock h5 {
    color: var(--text-primary);
    margin-bottom: 1rem;
    text-align: center;
}

.world-clock-item {
    background: var(--bg-primary);
    border-radius: var(--radius-md);
    padding: 1rem;
    text-align: center;
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
}

.world-clock-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.world-clock-item h6 {
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.world-clock-item .time {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-color);
    font-family: 'Courier New', monospace;
}

/* ===== Form Enhancements ===== */
.form-label {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.form-control,
.form-select {
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
    background: var(--bg-primary);
}

.form-control:focus,
.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(124, 58, 237, 0.25);
    outline: none;
}

.form-control-color {
    width: 100%;
    height: 50px;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    cursor: pointer;
}

footer .container .row{
    justify-content:center;
}

  footer .col-md-8{
        width: 100% !important;
        display: flex;
        justify-content: center;
    }

/* ===== Responsive Design ===== */
@media (max-width: 1200px) {
    .hero-visual {
        padding: 1.5rem 0;
        gap: 1.5rem;
    }

    .floating-card {
        min-width: 100px;
        padding: 0.75rem 1rem;
    }

    .floating-card h4 {
        font-size: 0.8rem;
    }

    .floating-card p {
        font-size: 0.7rem;
    }

    .conversion-card {
        min-width: 250px;
        padding: 1.5rem;
    }

    .hero-visual .tools-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 1rem;
    }

    .hero-visual .tool-card {
        padding: 1.25rem;
    }

    .hero-visual .tool-icon {
        width: 50px;
        height: 50px;
    }

    .hero-visual .tool-icon i {
        font-size: 1.25rem;
    }

    .hero-visual .conversion-example {
        gap: 1.5rem;
    }

    .hero-visual .conversion-example .conversion-input,
    .hero-visual .conversion-example .conversion-output {
        padding: 1.25rem;
    }

    .hero-visual .arrow-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }
}

@media (max-width: 992px) {
    .hero-section {
        padding: 4rem 0;
    }

    .hero-visual {
        padding: 1.5rem 0;
        gap: 1.5rem;
        margin-top: 2rem;
    }

    .conversion-card {
        min-width: 200px;
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
    }

    .conversion-input .value,
    .conversion-output .value {
        font-size: 1.5rem;
    }

    .floating-card.card-1,
    .floating-card.card-2,
    .floating-card.card-3,
    .floating-card.card-4,
    .floating-card.card-5 {
        position: static;
        display: inline-block;
        margin: 0.5rem;
        animation: none;
    }

    .hero-visual::after {
        content: '';
        display: block;
        margin-top: 2rem;
    }
}

@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
        text-align: center;
    }

    .hero-description {
        font-size: 1.125rem;
        text-align: center;
    }

    .hero-features {
        gap: 0.75rem;
        justify-content: center;
        flex-wrap: wrap;
    }

    .feature-badge {
        font-size: 0.8rem;
        padding: 0.5rem 0.75rem;
    }

    .hero-buttons {
        justify-content: center;
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .hero-buttons .btn {
        width: 100%;
        max-width: 280px;
    }

    .hero-visual {
        padding: 1rem 0;
        gap: 1rem;
    }

    .conversion-card {
        min-width: 180px;
        padding: 0.75rem;
    }

    .conversion-input .value,
    .conversion-output .value {
        font-size: 1.25rem;
    }

    .floating-card {
        min-width: 90px;
        padding: 0.5rem 0.75rem;
        margin: 0.25rem;
    }

    .floating-card i {
        font-size: 1.25rem;
    }

    .floating-card h4 {
        font-size: 0.75rem;
    }

    .floating-card p {
        font-size: 0.65rem;
    }

    .hero-visual .tools-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .hero-visual .tool-card {
        padding: 1rem;
    }

    .hero-visual .tool-card h4 {
        font-size: 0.9rem;
    }

    .hero-visual .tool-card p {
        font-size: 0.8rem;
    }

    .hero-visual .conversion-example {
        flex-direction: column;
        gap: 1rem;
    }

    .hero-visual .conversion-example .conversion-arrow {
        transform: rotate(90deg);
    }

    .hero-visual .conversion-demo-card {
        padding: 1.5rem;
    }

    .hero-visual .conversion-example .conversion-input,
    .hero-visual .conversion-example .conversion-output {
        padding: 1rem;
    }

    .hero-visual .conversion-example .conversion-input .value,
    .hero-visual .conversion-example .conversion-output .value {
        font-size: 1.5rem;
    }

    .hero-visual .arrow-icon {
        width: 45px;
        height: 45px;
        font-size: 1.125rem;
    }

    .converter-tabs .nav-pills {
        flex-direction: column;
        gap: 0.5rem;
        padding: 0.25rem;
    }

    .converter-tabs .nav-link {
        justify-content: center;
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
        min-width: auto;
    }

    .converter-tabs .nav-link i {
        font-size: 1rem;
    }

    .converter-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .converter-swap {
        width: 40px;
        height: 40px;
        font-size: 1rem;
        justify-self: center;
    }

    .converter-content {
        padding: 1.5rem 1rem;
    }

    .converter-header {
        padding: 1.5rem 1rem;
    }

    .converter-header h3 {
        font-size: 1.5rem;
    }

    .format-badges {
        gap: 0.25rem;
    }

    .format-badges .badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
}

@media (max-width: 576px) {
    .hero-section {
        padding: 3rem 0;
    }

    .hero-title {
        font-size: 2rem;
        line-height: 1.3;
    }

    .hero-description {
        font-size: 1rem;
        margin-bottom: 1.5rem;
    }

    .hero-features {
        flex-direction: column;
        gap: 0.5rem;
        margin-bottom: 2rem;
        align-items: center;
    }

    .feature-badge {
        font-size: 0.75rem;
        padding: 0.5rem 0.75rem;
        width: fit-content;
    }

    .stat-item {
        padding: 0.5rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .stat-label {
        font-size: 0.75rem;
    }

    .hero-visual {
        padding: 0.5rem 0;
        gap: 0.75rem;
    }

    .conversion-card {
        min-width: 150px;
        padding: 0.5rem;
    }

    .conversion-input .value,
    .conversion-output .value {
        font-size: 1rem;
    }

    .floating-card {
        min-width: 80px;
        padding: 0.4rem 0.6rem;
        margin: 0.2rem;
    }

    .floating-card i {
        font-size: 1rem;
        margin-bottom: 0.25rem;
    }

    .floating-card h4 {
        font-size: 0.7rem;
        margin-bottom: 0.1rem;
    }

    .floating-card p {
        font-size: 0.6rem;
    }

    .converters-section {
        padding: 3rem 0;
    }

    .section-header {
        margin-bottom: 2.5rem;
    }

    .section-header h2 {
        font-size: 1.75rem;
        margin-bottom: 0.75rem;
    }

    .section-header p {
        font-size: 1rem;
    }

    .converter-tabs .nav-pills {
        padding: 0.25rem;
    }

    .converter-tabs .nav-link {
        padding: 0.6rem 0.8rem;
        font-size: 0.85rem;
        flex-direction: column;
        gap: 0.25rem;
        width: 100%;
    }

    .converter-tabs .nav-link span {
        display: block;
    }

    .converter-content {
        padding: 1rem 0.75rem;
    }

    .converter-header {
        padding: 1.5rem 1rem;
    }

    .converter-header h3 {
        font-size: 1.25rem;
    }

    .converter-value {
        font-size: 1.25rem;
    }

    .world-clock {
        padding: 1rem;
    }

    .color-preview {
        height: 80px;
    }
}

/* ===== About Page Styles ===== */
.about-hero {
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
    padding: 6rem 0;
}

.about-hero h1 {
    font-size: 3rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
}

.about-hero .lead {
    font-size: 1.25rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

.about-hero-image {
    text-align: center;
    padding: 2rem;
}

.about-hero-image img {
    max-width: 100%;
    height: auto;
    filter: drop-shadow(0 10px 20px rgba(124, 58, 237, 0.1));
    transition: transform 0.3s ease;
}

.about-hero-image img:hover {
    transform: scale(1.05);
}

.mission-section {
    padding: 6rem 0;
    background: var(--bg-primary);
}

.mission-section h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 2rem;
}

.mission-section .lead {
    font-size: 1.125rem;
    color: var(--text-secondary);
    line-height: 1.7;
}

.features-section {
    padding: 6rem 0;
    background: var(--bg-secondary);
}

.features-section h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.feature-card {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    padding: 2rem;
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    height: 100%;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.feature-icon i {
    font-size: 2rem;
    color: white;
}

.feature-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.feature-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

.stats-section {
    padding: 6rem 0;
    background: var(--text-primary);
    color: white;
}

.stats-section h2 {
    color: white;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 2rem;
}

.stat-card {
    text-align: center;
    padding: 2rem;
}

.stat-card .stat-number {
    font-size: 3rem;
    font-weight: 700;
    color: var(--accent-color);
    display: block;
    line-height: 1;
    margin-bottom: 0.5rem;
}

.stat-card .stat-label {
    font-size: 1.125rem;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.team-section {
    padding: 6rem 0;
    background: var(--bg-primary);
}

.team-section h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 2rem;
}

.team-section .lead {
    font-size: 1.125rem;
    color: var(--text-secondary);
    line-height: 1.7;
    margin-bottom: 1.5rem;
}

.team-section p {
    color: var(--text-secondary);
    line-height: 1.7;
}

.technology-section {
    padding: 6rem 0;
    background: var(--bg-secondary);
}

.technology-section h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.tech-card {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    padding: 2rem;
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    height: 100%;
}

.tech-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.tech-card i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.tech-card h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.tech-card p {
    color: var(--text-secondary);
    margin: 0;
}

.cta-section {
    padding: 6rem 0;
    background: var(--bg-primary);
}

.cta-section h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
}

.cta-section .lead {
    font-size: 1.125rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* ===== Footer Styles (Updated) ===== */
.footer-links h5 {
    color: white;
    font-weight: 600;
    margin-bottom: 1rem;
    font-size: 1rem;
}

.footer-links ul {
    list-style: none;
    padding: 0;
}

.footer-links ul li {
    margin-bottom: 0.5rem;
}

.footer-links ul li a {
    color: #94a3b8;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.footer-links ul li a:hover {
    color: var(--primary-color);
    padding-left: 0.25rem;
}

.footer-social h5 {
    color: white;
    font-weight: 600;
    margin-bottom: 1rem;
    font-size: 1rem;
}

.social-links {
    display: flex;
    gap: 0.75rem;
}

.social-link {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #94a3b8;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-link:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 2rem;
    padding-top: 1.5rem;
}

.footer-bottom p {
    color: #94a3b8;
    margin: 0;
    font-size: 0.875rem;
}

.footer-badges {
    display: flex;
    gap: 0.75rem;
    justify-content: flex-end;
    flex-wrap: wrap;
}

.badge-item {
    background: rgba(255, 255, 255, 0.1);
    color: #94a3b8;
    padding: 0.375rem 0.75rem;
    border-radius: var(--radius-md);
    font-size: 0.8rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.375rem;
}

.badge-item i {
    color: var(--success-color);
}

/* ===== Contact Page Styles ===== */
.contact-hero {
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
    padding: 6rem 0;
    text-align: center;
}

.contact-hero h1 {
    font-size: 3rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
}

.contact-hero .lead {
    font-size: 1.25rem;
    color: var(--text-secondary);
}

.contact-section {
    padding: 6rem 0;
    background: var(--bg-primary);
}

.contact-form-container {
    background: var(--bg-secondary);
    border-radius: var(--radius-xl);
    padding: 3rem;
    box-shadow: var(--shadow-lg);
}

.contact-form-container h2 {
    font-size: 2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 2rem;
}

.contact-info {
    padding: 2rem 0;
}

.contact-info h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 2rem;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 2rem;
}

.contact-icon {
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    flex-shrink: 0;
}

.contact-icon i {
    font-size: 1.25rem;
    color: white;
}

.contact-details h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.contact-details p {
    color: var(--text-secondary);
    margin-bottom: 0.25rem;
    line-height: 1.5;
}

.social-contact {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
}

.social-contact h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.faq-section {
    padding: 6rem 0;
    background: var(--bg-secondary);
}

.faq-section h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
}

.accordion-item {
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    margin-bottom: 1rem;
    overflow: hidden;
}

.accordion-button {
    background: var(--bg-primary);
    color: var(--text-primary);
    font-weight: 500;
    border: none;
    padding: 1.5rem;
}

.accordion-button:not(.collapsed) {
    background: var(--primary-color);
    color: white;
    box-shadow: none;
}

.accordion-button:focus {
    box-shadow: none;
    border: none;
}

.accordion-body {
    background: var(--bg-primary);
    color: var(--text-secondary);
    line-height: 1.6;
    padding: 1.5rem;
}

/* ===== Legal Pages Styles ===== */
.main-content {
    padding: 6rem 0 4rem;
    min-height: 60vh;
}

.legal-document {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    padding: 3rem;
    margin-bottom: 2rem;
}

.legal-document h1 {
    color: var(--text-primary);
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.last-updated {
    color: var(--text-secondary);
    font-style: italic;
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid var(--border-color);
}

.legal-section {
    margin-bottom: 2.5rem;
}

.legal-section h2 {
    color: var(--text-primary);
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.legal-section h3 {
    color: var(--text-primary);
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    margin-top: 1.5rem;
}

.legal-section p {
    color: var(--text-secondary);
    line-height: 1.7;
    margin-bottom: 1rem;
}

.legal-section ul {
    color: var(--text-secondary);
    line-height: 1.7;
    margin-bottom: 1rem;
    padding-left: 1.5rem;
}

.legal-section ul li {
    margin-bottom: 0.5rem;
}

.legal-section a {
    color: var(--primary-color);
    text-decoration: none;
}

.legal-section a:hover {
    text-decoration: underline;
}

/* ===== Features Overview Section ===== */
.features-overview {
    padding: 6rem 0;
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
    position: relative;
}

.features-overview::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%237c3aed" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    pointer-events: none;
}

.feature-highlight {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    padding: 2.5rem;
    text-align: center;
    box-shadow: var(--shadow-lg);
    transition: all 0.3s ease;
    height: 100%;
    border: 1px solid var(--border-color);
    position: relative;
    z-index: 2;
}

.feature-highlight:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.feature-icon-large {
    width: 100px;
    height: 100px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    position: relative;
}

.feature-icon-large::before {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    opacity: 0.2;
    z-index: -1;
}

.feature-icon-large i {
    font-size: 2.5rem;
    color: white;
}

.feature-highlight h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.feature-highlight p {
    color: var(--text-secondary);
    line-height: 1.7;
    margin-bottom: 1.5rem;
}

.feature-list {
    list-style: none;
    padding: 0;
    text-align: left;
}

.feature-list li {
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
    position: relative;
    padding-left: 1.5rem;
}

.feature-list li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--success-color);
    font-weight: bold;
}



/* ===== Key Features Section ===== */
.key-features {
    padding: 4rem 0;
    background: var(--bg-secondary);
}

.feature-highlight-small {
    text-align: center;
    padding: 2rem 1rem;
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
    height: 100%;
}

.feature-highlight-small:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.feature-icon-small {
    width: 60px;
    height: 60px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
}

.feature-icon-small i {
    font-size: 1.5rem;
    color: white;
}

.feature-highlight-small h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.75rem;
}

.feature-highlight-small p {
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.5;
}

/* ===== Enhanced Footer Styles ===== */
.footer {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
    color: white;
    padding: 4rem 0 2rem;
    position: relative;
    margin-top: 4rem;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color), var(--info-color), var(--primary-color));
    animation: gradientShift 3s ease-in-out infinite alternate;
}

@keyframes gradientShift {
    0% { opacity: 0.8; }
    100% { opacity: 1; }
}

.footer-brand a {
    color: white;
    text-decoration: none;
    font-weight: 700;
    font-size: 1.75rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
}

.footer-brand a:hover {
    transform: translateY(-2px);
    color: var(--primary-light);
}

.footer-brand a i {
    font-size: 2rem;
    color: var(--primary-color);
    transition: all 0.3s ease;
}

.footer-brand a:hover i {
    color: var(--primary-light);
    transform: rotate(15deg);
}

.footer-brand p {
    color: #cbd5e1;
    line-height: 1.7;
    margin-bottom: 2rem;
    font-size: 1rem;
    max-width: 400px;
}

.footer-features {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-top: 1rem;
}

.footer-features span {
    color: #e2e8f0;
    font-size: 0.95rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem 0;
    transition: all 0.3s ease;
}

.footer-features span:hover {
    color: white;
    transform: translateX(5px);
}

.footer-features i {
    color: var(--success-color);
    font-size: 1rem;
    width: 20px;
    text-align: center;
}

.footer-links h5 {
    color: white;
    font-weight: 600;
    margin-bottom: 2rem;
    font-size: 1.25rem;
    position: relative;
    padding-bottom: 0.5rem;
}

.footer-links h5::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 30px;
    height: 2px;
    background: var(--primary-color);
    border-radius: 1px;
}

.footer-links ul {
    list-style: none;
    padding: 0;
}

.footer-links ul li {
    margin-bottom: 1rem;
}

.footer-links ul li a {
    color: #cbd5e1;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 0.95rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0;
}

.footer-links ul li a:hover {
    color: var(--primary-light);
    transform: translateX(8px);
}

.footer-links ul li a::before {
    content: '→';
    opacity: 0;
    transition: all 0.3s ease;
    transform: translateX(-10px);
}

.footer-links ul li a:hover::before {
    opacity: 1;
    transform: translateX(0);
}

/* Footer Contact Styles */
.footer-contact {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: #cbd5e1;
    font-size: 0.95rem;
    padding: 0.5rem 0;
    transition: all 0.3s ease;
}

.contact-item:hover {
    color: white;
    transform: translateX(5px);
}

.contact-item i {
    color: var(--primary-light);
    font-size: 1rem;
    width: 20px;
    text-align: center;
}

.footer-social h5 {
    color: white;
    font-weight: 600;
    margin-bottom: 1.5rem;
    font-size: 1.125rem;
}

.social-links {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-bottom: 2rem;
}

.social-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: var(--text-muted);
    text-decoration: none;
    transition: all 0.3s ease;
    padding: 0.5rem;
    border-radius: var(--radius-md);
}

.social-link:hover {
    color: var(--primary-color);
    background: rgba(124, 58, 237, 0.1);
    transform: translateX(0.25rem);
}

.social-link i {
    width: 20px;
    text-align: center;
}

.newsletter-signup h6 {
    color: white;
    font-weight: 600;
    margin-bottom: 1rem;
}

.newsletter-form {
    display: flex;
    gap: 0.5rem;
}

.newsletter-input {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-md);
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 0.875rem;
}

.newsletter-input::placeholder {
    color: var(--text-muted);
}

.newsletter-input:focus {
    outline: none;
    border-color: var(--primary-color);
    background: rgba(255, 255, 255, 0.15);
}

.newsletter-btn {
    padding: 0.75rem;
    background: var(--primary-color);
    border: none;
    border-radius: var(--radius-md);
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.newsletter-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.15);
    margin-top: 4rem;
    padding: 2rem;
    background: rgba(0, 0, 0, 0.2);
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.footer-bottom p {
    color: #cbd5e1;
    margin: 0;
    font-size: 0.9rem;
    line-height: 1.6;
}

.footer-bottom p a {
    color: var(--primary-light);
    text-decoration: none;
    margin: 0 0.5rem;
    transition: all 0.3s ease;
}

.footer-bottom p a:hover {
    color: white;
    text-decoration: underline;
}

.footer-badges {
    display: flex;
    gap: 1.5rem;
    justify-content: flex-end;
    flex-wrap: wrap;
    align-items: center;
}

.badge-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #cbd5e1;
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
    background: rgba(124, 58, 237, 0.1);
    border-radius: var(--radius-lg);
    border: 1px solid rgba(124, 58, 237, 0.2);
    transition: all 0.3s ease;
}

.badge-item:hover {
    background: rgba(124, 58, 237, 0.2);
    transform: translateY(-2px);
}

.badge-item i {
    color: var(--primary-light);
}

/* ===== Enhanced Timezone Converter Styles ===== */
.timezone-tools {
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    margin-top: 2rem;
}

.meeting-planner,
.quick-times {
    background: var(--bg-primary);
    border-radius: var(--radius-md);
    padding: 1.5rem;
    height: 100%;
}

.meeting-planner h5,
.quick-times h5 {
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.meeting-planner h5 i,
.quick-times h5 i {
    color: var(--primary-color);
}

.meeting-timezone-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem;
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    margin-bottom: 0.5rem;
    border: 1px solid var(--border-color);
}

.meeting-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.meeting-info strong {
    color: var(--text-primary);
    font-size: 0.9rem;
}

.meeting-time {
    color: var(--primary-color);
    font-weight: 600;
    font-family: 'Courier New', monospace;
}

.quick-time-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin-top: 1rem;
}

.quick-time-btn {
    flex: 1;
    min-width: 80px;
}

/* Enhanced World Clock */
.world-clock {
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    margin-top: 2rem;
}

.world-clock h5 {
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.world-clock h5 i {
    color: var(--primary-color);
}

.world-clock-item {
    background: var(--bg-primary);
    border-radius: var(--radius-md);
    padding: 1.5rem;
    text-align: center;
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.world-clock-item:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary-color);
}

.clock-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.flag {
    font-size: 1.5rem;
}

.world-clock-item h6 {
    color: var(--text-primary);
    font-weight: 600;
    margin: 0;
}

.world-clock-item .time {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    font-family: 'Courier New', monospace;
    margin-bottom: 0.5rem;
}

.world-clock-item small {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* ===== Responsive Enhancements ===== */
@media (max-width: 768px) {
    .features-overview {
        padding: 4rem 0;
    }

    .feature-highlight {
        padding: 2rem;
        margin-bottom: 2rem;
    }

    .feature-icon-large {
        width: 80px;
        height: 80px;
        margin-bottom: 1.5rem;
    }

    .feature-icon-large i {
        font-size: 2rem;
    }

    .feature-highlight h3 {
        font-size: 1.25rem;
    }

    .key-features {
        padding: 3rem 0;
    }

    .feature-highlight-small {
        padding: 1.5rem 1rem;
    }

    .feature-icon-small {
        width: 50px;
        height: 50px;
    }

    .feature-icon-small i {
        font-size: 1.25rem;
    }

    .footer {
        padding: 3rem 0 1rem;
    }

    .footer-brand a {
        font-size: 1.5rem;
        justify-content: center;
        text-align: center;
    }

    .footer-brand,
    .footer-links,
    .footer-social {
        text-align: center;
        margin-bottom: 2rem;
    }

    .footer-features {
        align-items: center;
    }

    .social-links {
        justify-content: center;
    }

    .footer-bottom .row {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .footer-badges {
        justify-content: center;
        margin-top: 1rem;
    }

    .timezone-tools {
        padding: 1rem;
    }

    .meeting-planner,
    .quick-times {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .quick-time-buttons {
        justify-content: center;
    }

    .quick-time-btn {
        flex: none;
        min-width: 70px;
    }

    .world-clock {
        padding: 1rem;
    }

    .world-clock-item {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .world-clock-item .time {
        font-size: 1.25rem;
    }
}

@media (max-width: 576px) {
    .feature-highlight {
        padding: 1.5rem;
    }

    .feature-icon-large {
        width: 60px;
        height: 60px;
    }

    .feature-icon-large i {
        font-size: 1.5rem;
    }

    .feature-highlight h3 {
        font-size: 1.125rem;
    }

    .feature-highlight-small {
        padding: 1rem;
    }

    .feature-highlight-small h4 {
        font-size: 1rem;
    }

    .feature-highlight-small p {
        font-size: 0.85rem;
    }

    .feature-icon-small {
        width: 45px;
        height: 45px;
    }

    .feature-icon-small i {
        font-size: 1.125rem;
    }

    .footer-brand a {
        font-size: 1.25rem;
        gap: 0.5rem;
    }

    .footer-brand a i {
        font-size: 1.5rem;
    }

    .social-link {
        padding: 0.25rem;
        font-size: 0.875rem;
    }

    .newsletter-input {
        font-size: 0.8rem;
        padding: 0.6rem;
    }

    .newsletter-btn {
        padding: 0.6rem;
    }

    .meeting-timezone-item {
        padding: 0.5rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .meeting-info {
        width: 100%;
    }

    .quick-time-btn {
        min-width: 60px;
        padding: 0.5rem;
        font-size: 0.8rem;
    }

    .world-clock-item .time {
        font-size: 1.125rem;
    }

    .flag {
        font-size: 1.25rem;
    }

  
}
